import { Field, FieldArray, Form, Formik } from 'formik'
import { Fragment, useEffect, useRef, useState } from 'react'
import { useSelector } from 'react-redux'
import * as Yup from 'yup'

import { CrossIcon } from '../../../../assets/icons/CrossIcon'
import { createClient, getClients, updateClient } from '../../../../logic/apis/client'
import { onlyText } from '../../../../shared/helpers/regex'
import {
  getDigitsFromPhone,
  getIdFromName,
  getKeysFromObjects,
  isSuccess,
  notify,
  splitFullName,
  getDataFromLocalStorage,
  getValueByKeyAndMatch,
} from '../../../../shared/helpers/util'
import { InputWithValidation } from '../../../../shared/inputWithValidation/InputWithValidation'
import { SharedPhone } from '../../../../shared/sharedPhone/SharedPhone'
import Toggle from '../../../../shared/toggle/Toggle'
import * as SharedStyled from '../../../../styles/styled'
import * as Styled from './style'
import { getCampaigns, getLeadSources } from '../../../../logic/apis/leadSource'
import CustomSelect from '../../../../shared/customSelect/CustomSelect'
import AutoCompleteAddress from '../../../../shared/autoCompleteAdress/AutoCompleteAddress'
import UnitSvg from '../../../../assets/newIcons/unitModal.svg'
import { Nue, StorageKey, usStatesShortNames } from '../../../../shared/helpers/constants'
import { colors } from '../../../../styles/theme'
import Button from '../../../../shared/components/button/Button'
import '../../../../shared/helpers/yupExtension'
import { CheckBox } from '../../../timeCard/components/editTimeCardPopUp/style'
import { SLoader } from '../../../../shared/components/loader/Loader'
import { getFormattedLeadSrcData, getLeadSrcDropdownId, getLeadSrcDropdownName } from '../../../leadSource/LeadSource'
import useFetch from '../../../../logic/apis/useFetch'
import AutoCompleteIndentation from '../../../../shared/autoCompleteIndentation/AutoCompleteIndentation'
import { I_LeadSource } from '../../../sales/AddOpportunityModal'
import { mergeSourceAndCampaignNames } from './AddNewContactModal'

import { Types } from '../../constant'
import { updateContact } from '../../../../logic/apis/contact'
import SearchableDropdown from '../../../../shared/searchableDropdown/SearchableDropdown'
import { fetchSearchReferer } from '../contactProfile/ContactProfile'
import { CustomModal } from '../../../../shared/customModal/CustomModal'
import ReferrerModal from '../../../Refferer/components/referrerModal/ReferrerModal'

interface I_AddNewClientModal {
  setShowEditClientModal: React.Dispatch<React.SetStateAction<boolean>>
  setDetailsUpdate?: React.Dispatch<React.SetStateAction<boolean>>
  detailsUpdate?: boolean
  setClientAutoFill?: any
  clientName?: string
  onClose?: () => void
  setCreatedClient?: any
  refererres?: any
  setReferrerValue?: any
  noLoadScript?: boolean
  clientData?: any
  setClientData: React.Dispatch<any>
  initFetchReferrers?: () => void
}
export interface I_Contacts {
  email: ''
  firstName: ''
  lastName: ''
  phone: ''
  notes: ''
}
export const EditContactModal = (props: I_AddNewClientModal) => {
  const {
    setShowEditClientModal,
    setDetailsUpdate,
    detailsUpdate,
    setClientAutoFill,
    clientName,
    onClose,
    setCreatedClient,
    refererres,
    setReferrerValue,
    noLoadScript,
    clientData,
    setClientData,
    initFetchReferrers,
  } = props
  /**
   * InitialValues is an interface declared here so that it can be used as a type for the useState hook
   */

  interface InitialValues {
    type: string
    businessName: string
    firstName: string
    lastName: string
    city: string
    street: string
    state: string
    zip: string
    phone: string
    email: string
    leadSourceName: string
    referredBy: string
    notes: string
    // campaign?: string
    // businessName?: string
  }

  /**
   * loading will be the loading state when performing the operations
   */
  const [isBusiness, setIsBusiness] = useState(clientData?.isBusiness ? true : false)
  const [loading, setLoading] = useState<boolean>(false)
  const [toggleAddress, setToggleAddress] = useState<boolean>(false)
  const [leadSrcData, setLeadSrcData] = useState([])

  const [checkedIndex, setCheckedIndex] = useState(null)
  const [selectedValue, setSelectedValue] = useState<any>(null)
  const globalSelector = useSelector((state: any) => state)
  const { currentMember, companySettingForAll } = globalSelector.company

  /**
   * This initialValues is the state which is passed to the Formik prop: initialValues
   */
  const [initialValues, setInitialValues] = useState<InitialValues>({
    type: Object.entries(Types).find(([_, v]) => v === clientData?.type)?.[0] || '',
    firstName: clientData?.firstName || '',
    lastName: clientData?.lastName || '',
    businessName: clientData?.businessName || '',
    city: clientData?.city || '',
    street: clientData?.street || '',
    state: clientData?.state || '',
    zip: clientData?.zip || '',
    phone: clientData?.phone || '',
    email: clientData?.email || '',
    leadSourceName: '',
    referredBy: getValueByKeyAndMatch('name', clientData?.referredBy, '_id', refererres) || '',
    notes: clientData?.notes || '',
    // businessName: '',
  })

  const [referrerModal, setShowReferrerModal] = useState(false)

  const inputRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus()
    }
  }, [inputRef])

  /**
   * AddCityModalSchema is a ValidationSchema which is passed to the Formik prop: validationSchema, here we add validation to all the input fields using yup
   */

  const AddCityModalSchema = Yup.object().shape({
    // firstName: Yup.string().when('businessName', (value, schema) => {
    //   if (value && isBusiness) return schema.optional()
    //   return schema.min(1, 'Too Short!').max(50, 'Too Long!').required('Required').matches(onlyText, 'Enter Valid Name')
    // }),
    firstName: Yup.string().required('Required'),
    lastName: isBusiness
      ? Yup.string().min(1, 'Too Short!').max(50, 'Too Long!')
      : Yup.string().min(1, 'Too Short!').max(50, 'Too Long!'),
    leadSourceName: Yup.string().required('Lead source is required!'),
    notes: Yup.string(),
    phone: Yup.string().min(10, 'Too Short!').required('Required'),
    email: Yup.string().trimEmail().email('Invalid email'),
    // businessName: Yup.string().min(2, 'Too Short!').max(50, 'Too Long!').matches(onlyText, 'Enter Valid Name'),
    // .required('Required'),
  })

  /**
   * handleSubmit is called when the form is submitted and this function needs to be passed to the Formik prop: onSubmit
   * @param submittedValues are the states which formik keeps track of and its type InitialValues is defined at the very top of this file
   */
  const handleSubmit = async (submittedValues: InitialValues, { resetForm }: any) => {
    try {
      const result = getLeadSrcDropdownId(submittedValues?.leadSourceName, leadSrcData)
      // const customLastName = submittedValues?.businessName?.split(clientFirstName)?.[1]?.trim()
      // const leadsourceId = getIdFromName(submittedValues?.leadSourceName, leadsrcDrop)
      const leadsourceId = result?.leadSourceId
      const campaignId = result?.campaignId || null
      const referredById =
        leadsourceId === '8c115e3c-2f31-4418-b0d6-fe1c0f5bbbc6'
          ? getIdFromName(submittedValues?.referredBy, refererres)
          : null
      setLoading(true)
      let { email, leadSourceName, contacts, ...restSubmitted }: any = submittedValues

      // remove cEmails key if empty
      if (email) restSubmitted.email = email

      let phone = getDigitsFromPhone(submittedValues.phone)
      let dataObj = {
        ...restSubmitted,
        fullName: `${submittedValues?.firstName?.trim()} ${submittedValues?.lastName?.trim() || ''}`?.trim(),
        firstName: submittedValues?.firstName?.trim(),
        lastName: isBusiness ? '' : submittedValues?.lastName?.trim() || '',
        businessName: isBusiness ? submittedValues?.businessName?.trim() : undefined,
        phone,
        type: Types[submittedValues.type as keyof typeof Types],
        leadSourceId: leadsourceId,
        createdBy: currentMember._id,
        referredBy: referredById,
        isBusiness,
        campaignId,
      }
      console.log({ dataObj })
      let response = await updateContact(dataObj, clientData._id)
      if (isSuccess(response)) {
        setClientData({ ...dataObj, _id: clientData._id })
        setClientAutoFill?.({
          clientName: isBusiness
            ? submittedValues?.businessName
            : `${submittedValues?.firstName} ${submittedValues?.lastName || ''}`?.trim(),
          phone: submittedValues?.phone,
          email: submittedValues?.email,
          fullName: isBusiness
            ? submittedValues?.businessName
            : `${submittedValues?.firstName} ${submittedValues?.lastName || ''}`?.trim(),
          sStreet: submittedValues?.street,
          sCity: submittedValues?.city,
          sState: submittedValues?.state,
          sZip: submittedValues?.zip,
          sLeadSourceName: leadSourceName,
          sLeadSourceId: leadsourceId,
          sCampaignId: campaignId,
          type: Types[submittedValues.type as keyof typeof Types] || undefined,
          referredBy: referredById,
        })
        notify('Contact Updated Successfully', 'success')
        resetForm()
        setDetailsUpdate?.((prev) => !prev)
        setLoading(false)
        setShowEditClientModal(false)
        setCreatedClient?.({ ...dataObj, _id: dataObj.clientId })
        onClose?.()
      } else {
        setLoading(false)
        notify(response?.data?.message, 'error')
      }
    } catch (error) {
      console.error('AddNewClientModal handleSubmit', error)
    }
  }

  const getLeadSrcData = async () => {
    try {
      const leadSourceResponse = await getLeadSources({ limit: '100', active: true }, false)
      if (leadSourceResponse?.data?.statusCode === 200) {
        let statusRes = leadSourceResponse?.data?.data?.leadSource
        setInitialValues((pre) => ({
          ...pre,
          leadSourceName:
            getLeadSrcDropdownName(clientData?.campaignId || clientData?.leadSourceId, statusRes)?.sourceName || '',
        }))
        setLeadSrcData(statusRes)
      } else notify(leadSourceResponse?.data?.message, 'error')
    } catch (err) {
      // notify('Failed to fetch lead sources!', 'error')
      console.log('Lead source fetch error', err)
    }
  }

  useEffect(() => {
    getLeadSrcData()
  }, [detailsUpdate])

  return (
    <Styled.AddNewClientModalContainer>
      <Formik
        initialValues={initialValues}
        enableReinitialize={true}
        onSubmit={handleSubmit}
        validationSchema={AddCityModalSchema}
        validateOnChange={true}
        validateOnBlur={false}
      >
        {({ values, errors, touched, resetForm, setFieldValue, handleChange }) => {
          useEffect(() => {
            if (clientName) {
              const { firstName, lastName } = splitFullName(clientName)
              setFieldValue('firstName', firstName)
              setFieldValue('lastName', lastName)
              // isBusiness && setFieldValue('businessName', `${firstName} ${lastName}`)
              // isBusiness && setClientFirstName(firstName)
            }
          }, [clientName, isBusiness])

          useEffect(() => {
            if (isBusiness) {
              if (values?.firstName !== '' || values?.lastName !== '')
                setFieldValue('firstName', `${values?.firstName?.trim()} ${values?.lastName?.trim() || ''}`)
              setFieldValue('lastName', '')
            }
            //  else {
            //   if (!isNewLead) {
            //     const { firstName, lastName } = values?.lastName
            //       ? splitFullName(`${values?.firstName} ${values?.lastName}`)
            //       : splitFullName(values?.firstName)
            //     setFieldValue('firstName', firstName)
            //     setFieldValue('lastName', lastName)
            //   }
            // }
          }, [isBusiness])

          // useEffect(() => {
          //   if (!isNewLead) {
          //     const { firstName, lastName } = splitFullName(
          //       values?.businessName?.trim() || `${values?.firstName} ${values?.lastName}`
          //     )
          //     setFieldValue('firstName', firstName)
          //     setFieldValue('lastName', lastName)
          //     isBusiness && setFieldValue('businessName', `${firstName} ${lastName}`)
          //     isBusiness && setClientFirstName(firstName)
          //   }
          // }, [isBusiness])

          return (
            <>
              <Styled.ModalHeaderContainer>
                <SharedStyled.FlexRow>
                  <img src={UnitSvg} alt="modal icon" />
                  <SharedStyled.FlexCol>
                    <Styled.ModalHeader>Edit Contact</Styled.ModalHeader>
                  </SharedStyled.FlexCol>
                </SharedStyled.FlexRow>
                <Styled.CrossContainer
                  onClick={() => {
                    setClientAutoFill?.({
                      clientName: '',
                      sStreet: '',
                      sCity: '',
                      sState: '',
                      sZip: '',
                      sLeadSourceName: '',
                      sLeadSource: '',
                      referredBy: '',
                      phone: '',
                      email: '',
                    })
                    setShowEditClientModal(false)
                  }}
                >
                  <CrossIcon />
                </Styled.CrossContainer>
              </Styled.ModalHeaderContainer>

              <SharedStyled.SettingModalContentContainer>
                {/* {(isLead && checkedIndex === -1) || !isLead ? ( */}
                <Form className="form">
                  <SharedStyled.Content
                    maxWidth="706px"
                    width="100%"
                    gap="6px"
                    disableBoxShadow={true}
                    noPadding={true}
                  >
                    <Toggle
                      title="Business"
                      customStyles={{ margin: '16px' }}
                      isToggled={isBusiness}
                      onToggle={() => {
                        setIsBusiness((prev) => !prev)
                      }}
                    />

                    {isBusiness ? (
                      <InputWithValidation
                        labelName="Business Name*"
                        stateName="businessName"
                        error={touched.businessName && errors.businessName ? true : false}
                        twoInput={true}
                      />
                    ) : (
                      <SharedStyled.TwoInputDiv>
                        <InputWithValidation
                          labelName="Primary First Name*"
                          stateName="firstName"
                          error={touched.firstName && errors.firstName ? true : false}
                          twoInput={true}
                        />
                        <InputWithValidation
                          labelName="Primary Last Name"
                          stateName="lastName"
                          error={touched.lastName && errors.lastName ? true : false}
                          twoInput={true}
                        />
                      </SharedStyled.TwoInputDiv>
                    )}

                    <SharedStyled.TwoInputDiv>
                      <SharedPhone
                        labelName="Primary Phone*"
                        stateName="phone"
                        value={values.phone || ''}
                        onChange={handleChange('phone')}
                        error={touched.phone && errors.phone ? true : false}
                      />

                      <InputWithValidation
                        labelName="Primary Email"
                        stateName="email"
                        error={touched.email && errors.email ? true : false}
                      />
                    </SharedStyled.TwoInputDiv>

                    {/* <CustomSelect
                      value={values.leadSourceName}
                      labelName="Lead Source*"
                      stateName="leadSourceName"
                      dropDownData={getKeysFromObjects(leadsrcDrop, 'name')?.sort()}
                      setFieldValue={setFieldValue}
                      setValue={() => {}}
                      margin="10px 0 0 0"
                      error={touched.leadSourceName && errors.leadSourceName ? true : false}
                    /> */}

                    <AutoCompleteIndentation
                      labelName="Lead Source*"
                      stateName={`leadSourceName`}
                      isLeadSource
                      dropdownHeight="300px"
                      error={touched.leadSourceName && errors.leadSourceName ? true : false}
                      borderRadius="0px"
                      setFieldValue={setFieldValue}
                      options={mergeSourceAndCampaignNames(leadSrcData)}
                      formatedOptions={getFormattedLeadSrcData(leadSrcData)}
                      value={values.leadSourceName!}
                      setValueOnClick={(val: string) => {
                        setFieldValue('leadSourceName', val)
                      }}
                      className="material-autocomplete"
                      isIndentation={true}
                    />

                    {values.leadSourceName === 'Referral' && (
                      <SharedStyled.FlexBox width="95%" justifyContent="end" margin="0 0 0 auto">
                        {/* <CustomSelect
                          labelName="Referrer"
                          stateName="referredBy"
                          error={touched.referredBy && errors.referredBy ? true : false}
                          setFieldValue={setFieldValue}
                          setValue={setReferrerValue}
                          value={values.referredBy}
                          dropDownData={refererres?.map((item: any) => item?.name || '')}
                          innerHeight="52px"
                          className="top"
                          maxWidth="95%"
                        /> */}

                        <SearchableDropdown
                          label="Referrer"
                          placeholder="Type to search"
                          searchFunction={(val) => {
                            return fetchSearchReferer(val, false)
                          }}
                          displayKey={'name'}
                          refererOptions={refererres?.slice(0, 20)}
                          onSelect={(item: any) => {
                            setFieldValue('referredBy', item?.name)
                          }}
                          selectedValue={values.referredBy}
                          handleBlur={() => {}}
                          resultExtractor={(res) => res?.data?.data?.referrers || []}
                          showAddOption
                          onAddClick={() => {
                            setShowReferrerModal?.(true)
                          }}
                        />
                      </SharedStyled.FlexBox>
                    )}

                    <Styled.GoogleSearchBox>
                      <AutoCompleteAddress
                        setFieldValue={setFieldValue}
                        street={'street'}
                        city={'city'}
                        state={'state'}
                        zip={'zip'}
                        sourceAddress={companySettingForAll?.address}
                        companyLatLong={companySettingForAll}
                        noLoadScript={noLoadScript ? noLoadScript : false}
                      />
                    </Styled.GoogleSearchBox>
                    {/* setToggleAddress */}
                    {!toggleAddress ? (
                      <div style={{ width: '100%' }}>
                        {values.street !== '' || values.city !== '' || values.state !== '' || values.zip !== '' ? (
                          <>
                            <SharedStyled.Text fontWeight="400">
                              <b>Address: </b> <br />
                              <span style={{ fontFamily: Nue.regular }}>
                                {values.street !== '' ? values.street : '--'}
                              </span>
                            </SharedStyled.Text>
                            <br />
                            <SharedStyled.Text fontWeight="400">
                              {/* <b>City : </b> */}
                              <span style={{ fontFamily: Nue.regular }}>{values.city},&nbsp;</span>
                              {/* <b>State : </b> */}
                              <span style={{ fontFamily: Nue.regular }}>{values.state},&nbsp;</span>
                              {/* <b>Zip : </b> */}
                              <span style={{ fontFamily: Nue.regular }}>{values.zip}</span>
                            </SharedStyled.Text>
                            &emsp;
                            <span
                              style={{ cursor: 'pointer', color: colors.darkBlue, fontFamily: Nue.medium }}
                              className="link"
                              onClick={() => setToggleAddress(!toggleAddress)}
                            >
                              Edit Manually
                            </span>
                          </>
                        ) : (
                          <>
                            <span
                              style={{ cursor: 'pointer', color: colors.darkBlue, fontFamily: Nue.medium }}
                              className="link"
                              onClick={() => setToggleAddress(!toggleAddress)}
                            >
                              Edit Manually
                            </span>
                          </>
                        )}
                      </div>
                    ) : (
                      <>
                        <InputWithValidation
                          labelName="Street Address"
                          stateName="street"
                          error={touched.street && errors.street ? true : false}
                        />

                        <SharedStyled.TwoInputDiv>
                          <InputWithValidation
                            labelName="City"
                            stateName="city"
                            error={touched.city && errors.city ? true : false}
                            twoInput={true}
                          />
                          <CustomSelect
                            dropDownData={usStatesShortNames}
                            setValue={() => {}}
                            stateName="state"
                            value={values.state}
                            // error={touched.weekStartDay && errors.weekStartDay ? true : false}
                            setFieldValue={setFieldValue}
                            labelName="State"
                            // innerHeight="52px"
                            margin="8px 0 0 0"
                          />
                          <InputWithValidation
                            labelName="Zip"
                            stateName="zip"
                            error={touched.zip && errors.zip ? true : false}
                          />
                        </SharedStyled.TwoInputDiv>
                      </>
                    )}
                    {/* <Styled.LabelDiv textAlign="left" width="100%" marginTop="8px">
                      Notes
                    </Styled.LabelDiv> */}
                    <Styled.TextArea
                      component="textarea"
                      placeholder="Notes"
                      as={Field}
                      name="notes"
                      marginTop="8px"
                      height="52px"
                    ></Styled.TextArea>
                    <SharedStyled.ButtonContainer marginTop="26px">
                      <Button type="submit" isLoading={loading}>
                        Save Changes
                      </Button>
                      <Button
                        type="button"
                        className="delete"
                        onClick={() => {
                          setShowEditClientModal(false)
                          setClientAutoFill?.({})
                        }}
                      >
                        Close
                      </Button>
                    </SharedStyled.ButtonContainer>
                  </SharedStyled.Content>
                </Form>
                {/* ) : null} */}
              </SharedStyled.SettingModalContentContainer>
            </>
          )
        }}
      </Formik>

      <CustomModal show={referrerModal}>
        <ReferrerModal
          onClose={() => {
            setShowReferrerModal(false)
          }}
          onComplete={() => {
            initFetchReferrers?.()
          }}
        />
      </CustomModal>
    </Styled.AddNewClientModalContainer>
  )
}
