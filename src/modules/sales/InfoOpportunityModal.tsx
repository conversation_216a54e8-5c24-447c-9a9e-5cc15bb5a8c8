import React from 'react'

import * as SharedStyled from '../../styles/styled'
import Button from '../../shared/components/button/Button'

interface I_ChangeLeadTypeModal {
  onClose: () => void
  onConfirm: () => void
  leadSource: string
  campaign: string
  daysAgo: string | number
}

const InfoOpportunityModal: React.FC<I_ChangeLeadTypeModal> = ({
  onClose,
  onConfirm,
  leadSource,
  campaign,
  daysAgo,
}) => {
  return (
    <div style={{ width: '450px' }}>
      <SharedStyled.SettingModalContentContainer>
        <SharedStyled.Content maxWidth="706px" width="100%" disableBoxShadow={true} noPadding={true}>
          <SharedStyled.FlexCol gap="16px">
            <SharedStyled.Text width="100%" fontSize="20px" textAlign="center" fontWeight="700">
              Most recent Lead information is {daysAgo} day(s) ago from
            </SharedStyled.Text>

            <SharedStyled.Text width="100%" fontSize="16px" textAlign="center" fontWeight="700">
              {leadSource} {campaign ? `: ${campaign}` : ''}
            </SharedStyled.Text>

            <SharedStyled.Text width="100%" fontSize="20px" textAlign="center" fontWeight="700">
              Use this info or create new?
            </SharedStyled.Text>

            <SharedStyled.FlexRow justifyContent="space-between">
              <Button type="button" onClick={onClose} className="gray">
                Use Existing
              </Button>
              <Button type="button" onClick={onConfirm}>
                Create New
              </Button>
            </SharedStyled.FlexRow>
          </SharedStyled.FlexCol>
        </SharedStyled.Content>
      </SharedStyled.SettingModalContentContainer>
    </div>
  )
}

export default InfoOpportunityModal
