import { Fragment, useEffect, useState } from 'react'
import * as Styled from './style'
import { renderMedia } from '../Media'
import { FlexCol, FlexRow, TooltipContainer } from '../../../styles/styled'
import ProfileInfo from '../../../shared/components/profileInfo/ProfileInfo'
import { dayjsFormat, isSuccess, notify } from '../../../shared/helpers/util'
import { CustomModal } from '../../../shared/customModal/CustomModal'
import Modal from '../../../shared/customModal/Modal'
import Button from '../../../shared/components/button/Button'
import { deleteMediaOpportunity, getDownloadUrl, updateMediaOpportunity } from '../../../logic/apis/media'
import { useNavigate, useParams } from 'react-router-dom'
import Tag from '../../../shared/components/tag'
import AutoComplete from '../../../shared/autoComplete/AutoComplete'
import EditMedia from './MediaEdit'
import { SmallLoaderCont } from '../../../shared/components/loader/style'
import CreateTagModal from '../../mediaSettings/components/CreateTagModal'
import { TransformComponent, TransformWrapper, useControls } from 'react-zoom-pan-pinch'
import FloatingActionButton, { WithZoomFeatures } from './FloatingActionButton'

interface IMediaPreview {
  allMedia: any[]
  setAllMedia?: Function
  onClose: () => void
  selectedIndex?: number
  isMediaSection?: boolean
  info?: any
  onSuccess?: Function
  isShareView?: boolean
  memberId?: string
  allTags?: string[]
  setTagBool?: React.Dispatch<React.SetStateAction<boolean>>
  isAllMedia?: boolean
}

const MediaPreview = (props: IMediaPreview) => {
  const {
    allMedia,
    onClose,
    selectedIndex,
    setAllMedia,
    isMediaSection = false,
    info,
    isShareView = false,
    onSuccess,
    allTags,
    memberId,
    setTagBool,
    isAllMedia,
  } = props

  console.log('props===[log]===>', props)
  const [currentIndex, setCurrentIndex] = useState(selectedIndex || 0)
  const { oppId: oppIdParams } = useParams()
  const [showDeleteModal, setShowDeleteModal] = useState(false)
  const [showTagModal, setShowTagModal] = useState(false)
  const [deleteLoading, setDeleteLoading] = useState(false)
  const [updateLoading, setUpdateLoading] = useState(false)
  const [selectedTags, setSelectedTags] = useState<string[]>([])
  const [showEditModal, setShowEditModal] = useState(false)
  const [imageDataUrl, setImageDataUrl] = useState(null)
  const [editLoading, setEditLoading] = useState(false)
  const [showAddModal, setShowAddModal] = useState(false)

  const [editClicked, setEditClicked] = useState(false)

  const oppId = isAllMedia ? allMedia[currentIndex]?.oppId : oppIdParams

  const handlePrevious = () => {
    setCurrentIndex((prev) => (prev === 0 ? allMedia.length - 1 : prev - 1))
  }
  const handleNext = () => {
    setCurrentIndex((prev) => (prev === allMedia.length - 1 ? 0 : prev + 1))
  }
  const handleDeleteClick = () => {
    setAllMedia?.((prev: any) => prev.filter((_: any, i: number) => i !== currentIndex))
    setCurrentIndex((index) => (index ? index - 1 : 0))
  }

  useEffect(() => {
    if (!allMedia?.length) {
      onClose()
    }
  }, [allMedia])

  const handleDeleteMedia = async () => {
    try {
      setDeleteLoading(true)
      const res = await deleteMediaOpportunity(allMedia[currentIndex]?.imageId)
      if (isSuccess(res)) {
        notify(res?.data?.data?.message, 'success')
        onSuccess?.()
        setShowDeleteModal(false)
        onClose()
      }
    } catch (error) {
    } finally {
      setDeleteLoading(false)
    }
  }
  const handleUpdateMedia = async () => {
    try {
      setUpdateLoading(true)
      const res = await updateMediaOpportunity(oppId!, [
        {
          _id: allMedia[currentIndex]?.imageId!,
          tags: selectedTags,
        },
      ])
      if (isSuccess(res)) {
        notify(res?.data?.data?.message, 'success')
        onSuccess?.()
        setShowTagModal(false)
        onClose()
      }
    } catch (error) {
    } finally {
      setUpdateLoading(false)
    }
  }
  useEffect(() => {
    if (allMedia[currentIndex]?.tags?.length) {
      setSelectedTags(allMedia[currentIndex]?.tags)
    }
  }, [allMedia[currentIndex]?.tags])

  const mediaType = allMedia[currentIndex]?.mimetype?.split('/')?.[0]

  useEffect(() => {
    if (mediaType && editClicked) {
      const fetchImage = async () => {
        setEditLoading(true)
        try {
          // const response = await fetch(allMedia[currentIndex]?.url)
          // const blob = await response.blob()
          // const reader: any = new FileReader()

          // reader.readAsDataURL(blob)
          // reader.onloadend = () => {
          //   setImageDataUrl(reader.result)
          // }

          const downloadResponse = await getDownloadUrl(allMedia[currentIndex]?.url, true)
          if (isSuccess(downloadResponse)) {
            const url = downloadResponse?.data?.data?.base64data
            setImageDataUrl(url)
          }
        } catch (error) {
          console.error('Failed to fetch image:', error)
          notify('Failed to load image, please try again later', 'error')
        } finally {
          setEditLoading(false)
        }
      }

      fetchImage()
    }
  }, [mediaType, editClicked])

  useEffect(() => {
    document.body.style.overflow = 'hidden'
    return () => {
      document.body.style.overflow = 'auto'
    }
  }, [])

  const FloatingButton = WithZoomFeatures(FloatingActionButton)

  return (
    <>
      <Styled.ViewerContainer>
        <Styled.MainImage>
          <>
            {!isMediaSection ? null : (
              <FlexCol className="info" alignItems="center">
                <div>
                  <p>
                    <b>{info?.po}</b> {info?.clientName}
                  </p>
                </div>
                {allMedia[currentIndex]?.tags?.length ? (
                  <div>
                    {allMedia[currentIndex]?.tags?.map((itm: string) => (
                      <Tag title={itm} key={itm} />
                    ))}
                  </div>
                ) : null}
              </FlexCol>
            )}
            <Styled.CloseButton
              onClick={() => {
                onClose()
              }}
              type="button"
            >
              &times;
            </Styled.CloseButton>

            {/* {allMedia?.length > 1 && !isMediaSection ? (
              <Styled.RemoveButton type="button" onClick={handleDeleteClick}>
                &#9940;
              </Styled.RemoveButton>
            ) : null} */}
            {currentIndex === 0 ? null : (
              <Styled.PrevButton type="button" onClick={handlePrevious}>
                &lt;
              </Styled.PrevButton>
            )}
            {allMedia?.length - 1 === currentIndex ? null : (
              <Styled.NextButton type="button" onClick={handleNext}>
                &gt;
              </Styled.NextButton>
            )}
            {mediaType === 'image' ? (
              <FlexCol justifyContent="flex-start" alignItems="center" className="userInfo">
                <Fragment>
                  <TransformWrapper>
                    <TransformComponent>
                      <img src={allMedia[currentIndex]?.url} className="preview-image" />
                    </TransformComponent>
                    {isMediaSection || isShareView ? (
                      <>
                        <FloatingButton
                          onDeleteClick={() => {
                            setShowDeleteModal(true)
                          }}
                          mediaUrl={allMedia?.[currentIndex]?.url}
                          isShareView={isShareView}
                          onTagClick={() => {
                            setShowTagModal(true)
                          }}
                          oppId={allMedia[currentIndex]?.oppId}
                          isAllMedia={isAllMedia}
                          onEditClick={() => {
                            setEditClicked(true)
                            setShowEditModal(true)
                          }}
                          mediaType={mediaType}
                          editLoading={editLoading}
                        />
                      </>
                    ) : null}
                  </TransformWrapper>
                </Fragment>

                {allMedia[currentIndex]?.user ? (
                  <Styled.UserInfoCont>
                    <ProfileInfo
                      data={{
                        users: {
                          firstName: allMedia[currentIndex]?.user,
                          imageUrl: allMedia[currentIndex]?.userImage,
                        },
                        id: allMedia[currentIndex]?.id,
                        email: dayjsFormat(allMedia[currentIndex]?.createdAt, 'MM-DD-YYYY - hh:mm a'),
                      }}
                      isMedia
                    />
                  </Styled.UserInfoCont>
                ) : null}
              </FlexCol>
            ) : (
              <>
                <TransformWrapper>
                  <TransformComponent>
                    {renderMedia(
                      allMedia[currentIndex]?.url,
                      allMedia[currentIndex]?.mimetype?.split('/')?.[0],
                      false,
                      true
                    )}
                  </TransformComponent>
                  {isMediaSection || isShareView ? (
                    <>
                      <FloatingButton
                        onDeleteClick={() => {
                          setShowDeleteModal(true)
                        }}
                        isShareView={isShareView}
                        onTagClick={() => {
                          setShowTagModal(true)
                        }}
                        oppId={allMedia[currentIndex]?.oppId}
                        onEditClick={() => {
                          setEditClicked(true)
                          setShowEditModal(true)
                        }}
                        mediaType={mediaType}
                        isAllMedia={isAllMedia}
                        editLoading={editLoading}
                      />
                    </>
                  ) : null}
                </TransformWrapper>

                {allMedia[currentIndex]?.user ? (
                  <Styled.UserInfoCont className={`run`}>
                    <ProfileInfo
                      data={{
                        users: {
                          firstName: allMedia[currentIndex]?.user,
                          imageUrl: allMedia[currentIndex]?.userImage,
                        },
                        id: allMedia[currentIndex]?.id,
                        email: dayjsFormat(allMedia[currentIndex]?.createdAt, 'MM-DD-YYYY - hh:mm a'),
                      }}
                      isMedia
                    />
                  </Styled.UserInfoCont>
                ) : null}
              </>
            )}
          </>
        </Styled.MainImage>

        <CustomModal show={showDeleteModal} className="media">
          <Modal
            title="Delete Media"
            onClose={() => {
              setShowDeleteModal(false)
            }}
          >
            <h3 className="text-center">Are you sure you want to delete this media?</h3>
            <FlexRow margin="20px 0 0 0">
              <Button
                onClick={() => {
                  setShowDeleteModal(false)
                }}
                className="gray"
                disabled={deleteLoading}
              >
                Cancel
              </Button>
              <Button onClick={handleDeleteMedia} isLoading={deleteLoading} className="delete">
                Delete
              </Button>
            </FlexRow>
          </Modal>
        </CustomModal>
        <CustomModal show={showTagModal} className="media overflow">
          <Modal
            title="Add Media Tags"
            onClose={() => {
              setShowTagModal(false)
            }}
          >
            <div style={{ marginTop: '10px' }}>
              {selectedTags?.map((itm: string) => (
                <Tag
                  key={itm}
                  title={itm}
                  showRemoveIcon
                  onClose={() => {
                    setSelectedTags((p) => p.filter((tag) => tag !== itm))
                  }}
                />
              ))}
            </div>
            <AutoComplete
              labelName="Tags"
              stateName="tags"
              options={allTags?.filter((tag) => !selectedTags?.includes(tag))!}
              setValueOnClick={(val: string) => {
                setSelectedTags((p) => [...p, val])
              }}
              showAddOption
              onAddClick={() => {
                setShowAddModal(true)
              }}
              dropdownHeight="300px"
              borderRadius="0px"
            />
            <FlexRow margin="20px 0 0 0">
              <Button
                onClick={() => {
                  setShowTagModal(false)
                }}
                className="gray"
                disabled={updateLoading}
              >
                Cancel
              </Button>
              <Button onClick={handleUpdateMedia} isLoading={updateLoading}>
                Update media
              </Button>
            </FlexRow>
          </Modal>
        </CustomModal>
        {showEditModal && imageDataUrl && (
          <EditMedia
            imageName={allMedia[currentIndex]?.name}
            imageUrl={imageDataUrl!}
            memberId={memberId}
            onEditSuccess={() => {
              setShowEditModal(false)
              onSuccess?.()
              onClose?.()
            }}
            currentImageData={allMedia[currentIndex]?.originalData}
            onClose={() => {
              setShowEditModal(false)
              onClose?.()
            }}
          />
        )}

        <CustomModal show={showAddModal} className="media">
          <CreateTagModal
            allData={[]}
            isMediaType={false}
            selectedTag={''}
            onCloseClick={() => {
              setShowAddModal(false)
            }}
            onAddSuccess={() => {
              setTagBool?.((p) => !p)
              setShowAddModal(false)
            }}
            allTags={allTags!}
          />
        </CustomModal>
      </Styled.ViewerContainer>
    </>
  )
}
export default MediaPreview
